#ifndef USER_PIC_H_
#define USER_PIC_H_

const uint16_t dongtu3[119][16384] = {    {
        0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E,
        0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E,
        0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E,    },
    {
        0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E,
        0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E,
        0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E,
        0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E,
        0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E,
        0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E,
        0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E,
        0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E,
        0xDEFE, 0xDEFE, 0xDEFE, 0xDEFE, 0xDEFE, 0xDEFE, 0xDEFE, 0xDEFE, 0xDEFE, 0xDEFE, 0xDEFE, 0xDEFE, 0xDEFE, 0xDEFE, 0xDEFE, 0xDEFE,
        0xDEFE, 0xDEFE, 0xDEFE, 0xDEFE, 0xDEFE, 0xDEFE, 0xDEFE, 0xDEFE, 0xDEFE, 0xDEFE, 0xDEFE, 0xDEFE, 0xDEFE, 0xDEFE, 0xDEFE, 0xDEFE,
        0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E, 0xDF1E,